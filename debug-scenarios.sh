#!/bin/bash

# Debug Scenarios Helper Script
# This script helps you test different scenarios for the v2 properties page

echo "🔧 Debug Scenarios for V2 Properties Page"
echo "=========================================="
echo ""
echo "Available scenarios:"
echo "1. Zero items (empty database)"
echo "2. Search results (shows results when searching)"
echo "3. No search results (shows no results when searching)"
echo "4. Normal mode (use real API)"
echo ""

read -p "Choose a scenario (1-4): " choice

case $choice in
    1)
        echo "🔄 Setting up ZERO ITEMS scenario..."
        export DEBUG_PREMISES_SCENARIO=zero
        echo "✅ Environment set to show zero items"
        echo "📝 Visit /v2/properties to see:"
        echo "   - Page title: 'Online properties'"
        echo "   - Count heading: '0 online properties'"
        echo "   - Search form HIDDEN (database is completely empty)"
        echo "   - Empty state message"
        ;;
    2)
        echo "🔄 Setting up SEARCH RESULTS scenario..."
        export DEBUG_PREMISES_SCENARIO=search_results
        echo "✅ Environment set to show search results"
        echo "📝 Visit /v2/properties and search for 'NE1' to see:"
        echo "   - Page title: 'Online properties matching 'NE1''"
        echo "   - Count heading: '3 online properties matching 'NE1''"
        echo "   - Mock search results displayed"
        ;;
    3)
        echo "🔄 Setting up NO SEARCH RESULTS scenario..."
        export DEBUG_PREMISES_SCENARIO=no_search_results
        echo "✅ Environment set to show no search results"
        echo "📝 Visit /v2/properties and search for 'XYZ' to see:"
        echo "   - Page title: 'Online properties matching 'XYZ''"
        echo "   - Count heading: '0 online properties matching 'XYZ''"
        echo "   - Search form REMAINS VISIBLE (user searched but no matches)"
        echo "   - Empty search results message"
        ;;
    4)
        echo "🔄 Setting up NORMAL MODE..."
        unset DEBUG_PREMISES_SCENARIO
        echo "✅ Environment cleared - using real API"
        echo "📝 Visit /v2/properties to see real data"
        ;;
    *)
        echo "❌ Invalid choice. Please run the script again."
        exit 1
        ;;
esac

echo ""
echo "🚀 Starting the development server..."
echo "Press Ctrl+C to stop the server"
echo ""

# Start the development server with the environment variable
npm run start:dev
