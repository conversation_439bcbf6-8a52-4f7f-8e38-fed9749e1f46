#!/bin/bash

# Debug Scenarios Helper Script
# This script helps you test different scenarios for the v2 properties page

echo "🔧 Debug Scenarios for V2 Properties Page"
echo "=========================================="
echo ""
echo "Available scenarios:"
echo "1. Zero items (empty database)"
echo "2. Search results (shows results when searching)"
echo "3. No search results (shows no results when searching)"
echo "4. Normal mode (use real API)"
echo ""

read -p "Choose a scenario (1-4): " choice

case $choice in
    1)
        echo "🔄 Setting up ZERO ITEMS scenario..."
        export DEBUG_PREMISES_SCENARIO=zero
        echo "✅ Environment set to show zero items"
        echo "📝 Visit /v2/properties to see empty state"
        ;;
    2)
        echo "🔄 Setting up SEARCH RESULTS scenario..."
        export DEBUG_PREMISES_SCENARIO=search_results
        echo "✅ Environment set to show search results"
        echo "📝 Visit /v2/properties and search for anything to see mock results"
        ;;
    3)
        echo "🔄 Setting up NO SEARCH RESULTS scenario..."
        export DEBUG_PREMISES_SCENARIO=no_search_results
        echo "✅ Environment set to show no search results"
        echo "📝 Visit /v2/properties and search for anything to see empty search results"
        ;;
    4)
        echo "🔄 Setting up NORMAL MODE..."
        unset DEBUG_PREMISES_SCENARIO
        echo "✅ Environment cleared - using real API"
        echo "📝 Visit /v2/properties to see real data"
        ;;
    *)
        echo "❌ Invalid choice. Please run the script again."
        exit 1
        ;;
esac

echo ""
echo "🚀 Starting the development server..."
echo "Press Ctrl+C to stop the server"
echo ""

# Start the development server with the environment variable
npm run start:dev
