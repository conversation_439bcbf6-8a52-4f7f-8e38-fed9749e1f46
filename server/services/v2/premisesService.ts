import type {
  Cas3BedspacePremisesSearchResult,
  Cas3PremisesSearchResult,
  Cas3PremisesSearchResults,
  Cas3PremisesStatus,
} from '@approved-premises/api'
import { PremisesSearchParameters, TableRow } from '@approved-premises/ui'
import type { PremisesClientV2 as PremisesClient, RestClientBuilder } from '../../data'

import { CallConfig } from '../../data/restClient'
import { cas3PremisesSearchResultFactory, cas3PremisesSearchResultsFactory } from '../../testutils/factories'

export default class PremisesService {
  constructor(protected readonly premisesClientFactory: RestClientBuilder<PremisesClient>) {}

  async searchData(
    callConfig: CallConfig,
    params: PremisesSearchParameters,
    status: Cas3PremisesStatus = 'online',
  ): Promise<Cas3PremisesSearchResults & { tableRows: Array<TableRow> }> {
    const premisesClient = this.premisesClientFactory(callConfig)

    // DEBUG: Force different scenarios for testing using environment variables
    // Set DEBUG_PREMISES_SCENARIO=zero|search_results|no_search_results to test different scenarios
    const debugScenario = process.env.DEBUG_PREMISES_SCENARIO

    let premises

    if (debugScenario === 'zero') {
      // SCENARIO 1: Zero items in database (no properties at all)
      premises = cas3PremisesSearchResultsFactory.build({
        results: [],
        totalPremises: 0,
        totalOnlineBedspaces: 0,
        totalUpcomingBedspaces: 0
      })
    } else if (debugScenario === 'search_results' && params.postcodeOrAddress) {
      // SCENARIO 2: Has results after search (when postcodeOrAddress is provided)
      const mockResults = cas3PremisesSearchResultFactory.buildList(2, {}, {
        transient: {
          addressLine1: ['123 Test St', '456 Test Ave'],
          town: ['Test Town', 'Test City'],
          postcode: ['TE1 1ST', 'TE2 2ST'],
          reference: ['Test Property 1', 'Test Property 2']
        }
      })
      premises = cas3PremisesSearchResultsFactory.build({
        results: mockResults,
        totalPremises: 2,
        totalOnlineBedspaces: 3,
        totalUpcomingBedspaces: 0
      })
    } else if (debugScenario === 'no_search_results' && params.postcodeOrAddress) {
      // SCENARIO 3: No results after search (search term provided but no matches)
      premises = cas3PremisesSearchResultsFactory.build({
        results: [],
        totalPremises: 0,
        totalOnlineBedspaces: 0,
        totalUpcomingBedspaces: 0
      })
    } else {
      // DEFAULT: Use real API call
      premises = await premisesClient.search(params.postcodeOrAddress ?? '', status)
    }

    const tableRows =
      premises.results === undefined
        ? []
        : premises.results.map(entry => {
            return [
              this.htmlValue(this.formatAddress(entry)),
              this.htmlValue(this.formatBedspaces(entry)),
              this.textValue(entry.pdu),
              this.htmlValue(`<a href="#">Manage</a>`),
            ]
          })

    return {
      ...premises,
      tableRows,
    }
  }

  async tableRows(callConfig: CallConfig, params: PremisesSearchParameters): Promise<Array<TableRow>> {
    const searchData = await this.searchData(callConfig, params)
    return searchData.tableRows
  }

  private textValue(value: string) {
    return { text: value }
  }

  private htmlValue(value: string) {
    return { html: value }
  }

  private formatAddress(premises: Cas3PremisesSearchResult): string {
    return [premises.addressLine1, premises.addressLine2, premises.town, premises.postcode]
      .filter(line => line !== undefined && line !== null)
      .map(line => line.trim())
      .filter(line => line !== '')
      .join('<br />')
  }

  private formatBedspace(bedspace: Cas3BedspacePremisesSearchResult): string {
    const archived =
      bedspace.status === 'archived' ? ` <strong class="govuk-tag govuk-tag--grey">Archived</strong>` : ''

    return `<a href="#">${bedspace.reference}</a>${archived}`
  }

  private formatBedspaces(premises: Cas3PremisesSearchResult): string {
    if (premises.bedspaces === undefined || premises.bedspaces.length === 0) {
      return `No bedspaces<br /><a href="#">Add a bedspace</a>`
    }

    return premises.bedspaces.map(this.formatBedspace).join('<br />')
  }
}
